// cron/config.ts
// Centralized configuration for the cron service

/**
 * Configuration for the cron service
 * Values are loaded from environment variables with sensible defaults
 */
export const config = {
  // Database configuration
  database: {
    url: process.env.DATABASE_URL,
    schemas: ["web", "workers"],
  },

  // Redis configuration
  redis: {
    url: process.env.REDIS_URL,
    streamPrefix: "auto-apply::",
    consumerGroup: "cron-service",
    consumerName: `cron-worker-${process.pid}`,
  },

  // Circuit breaker configuration
  // UPDATED: Reduced thresholds to prevent 100% resource usage
  circuitBreaker: {
    // Reduced memory threshold to prevent system overload
    memoryThresholdPercent: process.env.CIRCUIT_BREAKER_MEMORY_THRESHOLD
      ? parseInt(process.env.CIRCUIT_BREAKER_MEMORY_THRESHOLD)
      : 75, // Reduced from 90% to 75% to prevent overload
    // Reduced CPU threshold to prevent system overload
    cpuThresholdPercent: process.env.CIRCUIT_BREAKER_CPU_THRESHOLD
      ? parseInt(process.env.CIRCUIT_BREAKER_CPU_THRESHOLD)
      : 75, // Reduced from 90% to 75% to prevent overload
    errorThresholdCount: process.env.CIRCUIT_BREAKER_ERROR_THRESHOLD
      ? parseInt(process.env.CIRCUIT_BREAKER_ERROR_THRESHOLD)
      : 5,
    resetTimeoutMs: process.env.CIRCUIT_BREAKER_RESET_TIMEOUT
      ? parseInt(process.env.CIRCUIT_BREAKER_RESET_TIMEOUT)
      : 60000, // 1 minute
    checkIntervalMs: process.env.CIRCUIT_BREAKER_CHECK_INTERVAL
      ? parseInt(process.env.CIRCUIT_BREAKER_CHECK_INTERVAL)
      : 300000, // Increased to 5 minutes to drastically reduce resource usage
    consecutiveReadingsForOpen: process.env
      .CIRCUIT_BREAKER_CONSECUTIVE_READINGS_OPEN
      ? parseInt(process.env.CIRCUIT_BREAKER_CONSECUTIVE_READINGS_OPEN)
      : 2,
    consecutiveReadingsForClose: process.env
      .CIRCUIT_BREAKER_CONSECUTIVE_READINGS_CLOSE
      ? parseInt(process.env.CIRCUIT_BREAKER_CONSECUTIVE_READINGS_CLOSE)
      : 5, // Increased from 3 to 5 for more stability
    consecutiveReadingsForDegraded: process.env
      .CIRCUIT_BREAKER_CONSECUTIVE_READINGS_DEGRADED
      ? parseInt(process.env.CIRCUIT_BREAKER_CONSECUTIVE_READINGS_DEGRADED)
      : 3, // Increased from 1 to 3 to prevent rapid oscillation
    // Adjusted degraded thresholds to be lower than main thresholds
    degradedMemoryThresholdPercent: process.env
      .CIRCUIT_BREAKER_DEGRADED_MEMORY_THRESHOLD
      ? parseInt(process.env.CIRCUIT_BREAKER_DEGRADED_MEMORY_THRESHOLD)
      : 60, // Reduced from 85% to 60% to be well below main threshold
    degradedCpuThresholdPercent: process.env
      .CIRCUIT_BREAKER_DEGRADED_CPU_THRESHOLD
      ? parseInt(process.env.CIRCUIT_BREAKER_DEGRADED_CPU_THRESHOLD)
      : 60, // Reduced from 85% to 60% to be well below main threshold
  },

  // Job configurations
  jobs: {
    // Domain update job configuration
    domainUpdate: {
      batchSize: process.env.DOMAIN_UPDATE_BATCH_SIZE
        ? parseInt(process.env.DOMAIN_UPDATE_BATCH_SIZE)
        : 2,
      delayBetweenBatchesMs: process.env.DOMAIN_UPDATE_BATCH_DELAY
        ? parseInt(process.env.DOMAIN_UPDATE_BATCH_DELAY)
        : 15000, // 15 seconds
      maxCompanies: process.env.DOMAIN_UPDATE_MAX_COMPANIES
        ? parseInt(process.env.DOMAIN_UPDATE_MAX_COMPANIES)
        : 100,
      delayAfterSuccessMs: process.env.DOMAIN_UPDATE_SUCCESS_DELAY
        ? parseInt(process.env.DOMAIN_UPDATE_SUCCESS_DELAY)
        : 10000, // 10 seconds
      delayAfterFailureMs: process.env.DOMAIN_UPDATE_FAILURE_DELAY
        ? parseInt(process.env.DOMAIN_UPDATE_FAILURE_DELAY)
        : 5000, // 5 seconds
      delayAfterErrorMs: process.env.DOMAIN_UPDATE_ERROR_DELAY
        ? parseInt(process.env.DOMAIN_UPDATE_ERROR_DELAY)
        : 3000, // 3 seconds
      lockTimeoutSeconds: process.env.DOMAIN_UPDATE_LOCK_TIMEOUT
        ? parseInt(process.env.DOMAIN_UPDATE_LOCK_TIMEOUT)
        : 3600, // 1 hour
    },

    // Job enrichment configuration
    enrichJobDetails: {
      batchSize: process.env.ENRICH_JOBS_BATCH_SIZE
        ? parseInt(process.env.ENRICH_JOBS_BATCH_SIZE)
        : 100, // Dynamic default, can be overridden
      concurrency: process.env.ENRICH_JOBS_CONCURRENCY
        ? parseInt(process.env.ENRICH_JOBS_CONCURRENCY)
        : 2,
      maxMemoryThresholdMB: process.env.ENRICH_JOBS_MAX_MEMORY
        ? parseInt(process.env.ENRICH_JOBS_MAX_MEMORY)
        : 1500, // 1.5GB default
      warningMemoryThresholdMB: process.env.ENRICH_JOBS_WARNING_MEMORY
        ? parseInt(process.env.ENRICH_JOBS_WARNING_MEMORY)
        : 1000, // 1GB warning threshold
      lockTimeoutSeconds: process.env.ENRICH_JOBS_LOCK_TIMEOUT
        ? parseInt(process.env.ENRICH_JOBS_LOCK_TIMEOUT)
        : 3600, // 1 hour
      enableDynamicBatching:
        process.env.ENRICH_JOBS_DYNAMIC_BATCHING !== "false", // Default enabled
      maxJobsPerRun: process.env.ENRICH_JOBS_MAX_PER_RUN
        ? parseInt(process.env.ENRICH_JOBS_MAX_PER_RUN)
        : 0, // 0 = no limit, process all available
    },

    // Cleanup job configuration
    cleanup: {
      jobAgeDays: process.env.CLEANUP_JOB_AGE_DAYS
        ? parseInt(process.env.CLEANUP_JOB_AGE_DAYS)
        : 30,
      batchSize: process.env.CLEANUP_BATCH_SIZE
        ? parseInt(process.env.CLEANUP_BATCH_SIZE)
        : 1000,
    },
  },

  // Email configuration
  email: {
    fromAddress: process.env.EMAIL_FROM_ADDRESS ?? "<EMAIL>",
    adminEmail: process.env.ADMIN_EMAIL ?? "<EMAIL>",
    sendGridApiKey: process.env.SENDGRID_API_KEY,
  },

  // System configuration
  system: {
    timezone: process.env.TIMEZONE ?? "America/New_York",
    environment: process.env.NODE_ENV ?? "development",
    isProduction: process.env.NODE_ENV === "production",
    logLevel: process.env.LOG_LEVEL ?? "info",
  },
};

export default config;
