# Cloudflare R2 Storage Setup Guide

This guide will help you set up Cloudflare R2 storage for company logos and other file assets.

## Prerequisites

- Cloudflare account
- R2 subscription (free tier: 10GB storage + 1M operations/month)

## Step 1: Create R2 Bucket

1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. Navigate to **R2 Object Storage**
3. Click **Create bucket**
4. Name your bucket: `auto-apply-assets`
5. Choose location: **Automatic** (recommended)
6. Click **Create bucket**

## Step 2: Generate API Token

1. In R2 dashboard, go to **Manage R2 API tokens**
2. Click **Create API token**
3. Configure token:
   - **Token name**: `auto-apply-r2-access`
   - **Permissions**: 
     - ✅ Object Read
     - ✅ Object Write
     - ✅ Object Delete
   - **Bucket**: Select `auto-apply-assets`
   - **TTL**: No expiry (or set as needed)
4. Click **Create API token**
5. **IMPORTANT**: Copy the Access Key ID and Secret Access Key immediately

## Step 3: Configure Environment Variables

Add these to your `.env` file:

```bash
# Cloudflare R2 Storage Configuration
R2_ACCOUNT_ID=7efc1bf67e7d23f5683e06d0227c883f
R2_ENDPOINT=https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com
R2_ACCESS_KEY_ID=your_actual_access_key_id_here
R2_SECRET_ACCESS_KEY=your_actual_secret_access_key_here
R2_BUCKET_NAME=auto-apply-assets
R2_CUSTOM_DOMAIN=assets.yourdomain.com  # Optional: for custom domain
```

## Step 4: Install Required Dependencies

```bash
cd cron
npm install @aws-sdk/client-s3 sharp
```

## Step 5: Test the Setup

Run the company enrichment job to test logo upload:

```bash
npm run enrich-company-data
```

## File Structure in R2

The system will organize files like this:

```
auto-apply-assets/
├── company-logos/
│   ├── {companyId}/
│   │   ├── {timestamp}-{randomId}-{name}-original.webp
│   │   ├── {timestamp}-{randomId}-{name}-optimized.webp
│   │   └── {timestamp}-{randomId}-{name}-thumbnail.webp
├── resumes/
│   └── {userId}/
│       └── {timestamp}-{randomId}-{name}.pdf
└── profile-pictures/
    └── {userId}/
        ├── {timestamp}-{randomId}-{name}-original.webp
        ├── {timestamp}-{randomId}-{name}-optimized.webp
        └── {timestamp}-{randomId}-{name}-thumbnail.webp
```

## Image Processing

The system automatically:
- ✅ Downloads logos from company websites or Clearbit API
- ✅ Converts to WebP format for optimal compression
- ✅ Creates 3 sizes: original (400x200), optimized (200x100), thumbnail (50x25)
- ✅ Stores all variants in R2
- ✅ Updates database with optimized logo URL

## Usage Monitoring

Monitor your R2 usage:
1. Go to R2 dashboard
2. Check **Analytics** tab
3. Monitor:
   - Storage usage (10GB free)
   - Operations count (1M free/month)
   - Bandwidth (free egress to Cloudflare CDN)

## Custom Domain (Optional)

To use a custom domain for faster delivery:

1. Add a CNAME record: `assets.yourdomain.com` → `auto-apply-assets.7efc1bf67e7d23f5683e06d0227c883f.r2.dev`
2. Update `R2_CUSTOM_DOMAIN` in your `.env`
3. The system will automatically use your custom domain for public URLs

## Security Notes

- ✅ API tokens are scoped to specific bucket and permissions
- ✅ Files are stored with unique, non-guessable names
- ✅ No public listing of bucket contents
- ✅ Access logs available in Cloudflare dashboard

## Troubleshooting

### Common Issues:

1. **403 Forbidden**: Check API token permissions
2. **Bucket not found**: Verify bucket name in environment variables
3. **Upload fails**: Check file size limits and content types
4. **Slow uploads**: Consider using custom domain for better routing

### Debug Commands:

```bash
# Test R2 connection
node -e "
const { S3Client, ListBucketsCommand } = require('@aws-sdk/client-s3');
const client = new S3Client({
  endpoint: 'https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com',
  region: 'auto',
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
  },
});
client.send(new ListBucketsCommand({})).then(console.log).catch(console.error);
"
```

## Cost Optimization

- Free tier includes 10GB storage + 1M operations
- WebP compression reduces storage by ~30-50%
- Thumbnail generation reduces bandwidth usage
- Custom domain provides free CDN acceleration

## Next Steps

1. Set up the API token
2. Configure environment variables
3. Run the enrichment job
4. Monitor usage in Cloudflare dashboard
5. Consider setting up custom domain for production
