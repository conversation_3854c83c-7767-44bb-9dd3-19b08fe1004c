// cron/lib/enrichCompanyData.ts
// Functions to enrich company data with domains, websites, and logos

import { PrismaClient } from "@prisma/client";
import dns from "dns";
import { promisify } from "util";
import { logger } from "../utils/logger";
import { chromium } from "playwright";
import { handleCaptchaIfPresent } from "../scripts/improvedCaptchaSolver";

const prisma = new PrismaClient();
const dnsLookup = promisify(dns.lookup);

// Delay function
const delay = (ms: number, log: boolean = true) => {
  if (log) {
    logger.info(`⏳ Waiting ${ms}ms...`);
  }
  return new Promise((resolve) => setTimeout(resolve, ms));
};

// Validate domain format
function isValidDomainFormat(domain: string): boolean {
  const domainRegex =
    /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return domainRegex.test(domain) && domain.length <= 253;
}

// Verify domain exists via DNS lookup
async function verifyDomainExists(domain: string): Promise<boolean> {
  try {
    await dnsLookup(domain);
    return true;
  } catch (error) {
    return false;
  }
}

// Extract and verify domain for a company
export async function extractAndVerifyDomain(
  companyName: string,
  companyId: string
): Promise<string | null> {
  logger.info(`🔍 Extracting domain for company: ${companyName}`);

  try {
    // First try to search for the company domain
    const domains = await searchCompanyDomains(companyName);

    if (domains.length === 0) {
      logger.warn(`No domains found for company: ${companyName}`);
      return null;
    }

    // Verify each domain until we find a valid one
    for (const domain of domains) {
      if (isValidDomainFormat(domain)) {
        const exists = await verifyDomainExists(domain);
        if (exists) {
          logger.info(`✅ Verified domain for ${companyName}: ${domain}`);
          return domain;
        } else {
          logger.warn(`❌ Domain does not exist: ${domain}`);
        }
      } else {
        logger.warn(`❌ Invalid domain format: ${domain}`);
      }
    }

    logger.warn(`No valid domains found for company: ${companyName}`);
    return null;
  } catch (error) {
    logger.error(`Error extracting domain for ${companyName}:`, error);
    return null;
  }
}

// Search for company domains using Bing
async function searchCompanyDomains(companyName: string): Promise<string[]> {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({
    userAgent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  });
  const page = await context.newPage();

  try {
    // Construct Bing search query
    const searchQuery = encodeURIComponent(`${companyName} official website`);
    const bingUrl = `https://www.bing.com/search?q=${searchQuery}&setlang=en-US&cc=US&ensearch=1`;

    logger.info(`🌐 Navigating to Bing: ${bingUrl}`);

    await page.goto(bingUrl, {
      waitUntil: "domcontentloaded",
      timeout: 30000,
    });

    // Wait for search results to load
    await delay(3000);

    // Check for CAPTCHA
    const { captchaDetected } = await handleCaptchaIfPresent(page);
    if (captchaDetected) {
      logger.warn(`⚠️ CAPTCHA detected for company search: ${companyName}`);
      return [];
    }

    // Extract domains from search results
    const domains = await page.evaluate(() => {
      const results = Array.from(document.querySelectorAll(".b_algo"));
      return results
        .slice(0, 5)
        .map((result) => {
          const link = result.querySelector("cite")?.textContent || "";
          try {
            // Extract domain from URL
            const url = new URL(
              link.startsWith("http") ? link : `https://${link}`
            );
            return url.hostname.replace(/^www\./, "");
          } catch {
            // Try to extract domain from text if URL parsing fails
            const match = link.match(/(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
            return match ? match[1] : "";
          }
        })
        .filter(Boolean);
    });

    return [...new Set(domains)]; // Remove duplicates
  } catch (error) {
    logger.error(`Error searching Bing for company domain: ${error}`);
    return [];
  } finally {
    await browser.close();
  }
}

// Check if domain already exists for another company
export async function checkDomainExists(
  domain: string,
  currentCompanyId: string,
  client: any
): Promise<{ exists: boolean; existingCompany?: any }> {
  const existingCompany = await client.company.findFirst({
    where: {
      domain: domain,
      id: { not: currentCompanyId },
    },
    select: { id: true, name: true },
  });

  return {
    exists: !!existingCompany,
    existingCompany,
  };
}

// Ensure websites are set based on domains
export async function ensureWebsitesFromDomains(
  prisma: PrismaClient
): Promise<number> {
  const companiesWithDomainNoWebsite = await prisma.company.findMany({
    where: {
      domain: { not: null },
      website: null,
    },
    select: {
      id: true,
      name: true,
      domain: true,
    },
  });

  let websitesAdded = 0;
  let errorCount = 0;

  logger.info(
    `Found ${companiesWithDomainNoWebsite.length} companies with domains but no websites`
  );

  for (const company of companiesWithDomainNoWebsite) {
    try {
      // Validate domain format before creating website
      if (!company.domain || !isValidDomainFormat(company.domain)) {
        logger.warn(
          `Invalid domain format for company ${company.name}: ${company.domain}`
        );
        continue;
      }

      const website = `https://www.${company.domain}`;

      try {
        await prisma.company.update({
          where: { id: company.id },
          data: { website },
        });
        logger.info(`Added website ${website} to company ${company.name}`);
        websitesAdded++;
      } catch (updateError: any) {
        // Check if this is a unique constraint error
        if (updateError.code === "P2002") {
          logger.warn(
            `Unique constraint error for company ${company.name} when adding website ${website}`
          );

          // Try to find the conflicting company
          const conflictingCompany = await prisma.company.findFirst({
            where: {
              website: website,
              id: { not: company.id },
            },
            select: { id: true, name: true },
          });

          if (conflictingCompany) {
            logger.warn(
              `Website ${website} already exists for company ${conflictingCompany.name}`
            );
          }

          errorCount++;
        } else {
          throw updateError;
        }
      }

      // Add a small delay between updates to avoid overwhelming the database
      await delay(1000, false);
    } catch (error) {
      logger.error(`Failed to add website for company ${company.name}:`, error);
      errorCount++;
    }
  }

  logger.info(
    `Website update summary: ${websitesAdded} added, ${errorCount} errors, ${companiesWithDomainNoWebsite.length - websitesAdded - errorCount} skipped`
  );
  return websitesAdded;
}

// Extract company logo from website
async function extractCompanyLogoFromWebsite(
  website: string
): Promise<string | null> {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({
    userAgent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  });
  const page = await context.newPage();

  try {
    logger.info(`🎨 Extracting logo from: ${website}`);

    await page.goto(website, {
      waitUntil: "domcontentloaded",
      timeout: 15000,
    });

    // Wait for page to load
    await delay(2000, false);

    // Try multiple strategies to find the logo
    const logoUrl = await page.evaluate((siteUrl) => {
      const url = new URL(siteUrl);
      const baseUrl = `${url.protocol}//${url.hostname}`;

      // Strategy 1: Look for common logo selectors
      const logoSelectors = [
        'img[alt*="logo" i]',
        'img[class*="logo" i]',
        'img[id*="logo" i]',
        ".logo img",
        "#logo img",
        "header img",
        ".header img",
        ".navbar img",
        ".nav img",
        '[class*="brand"] img',
        '[class*="header"] img:first-of-type',
      ];

      for (const selector of logoSelectors) {
        const img = document.querySelector(selector) as HTMLImageElement;
        if (img && img.src) {
          // Make sure it's not a tiny icon or placeholder
          if (img.width >= 50 || img.height >= 30) {
            return img.src.startsWith("http")
              ? img.src
              : new URL(img.src, baseUrl).href;
          }
        }
      }

      // Strategy 2: Look for favicon as fallback
      const favicon = document.querySelector(
        'link[rel*="icon"]'
      ) as HTMLLinkElement;
      if (favicon && favicon.href) {
        return favicon.href.startsWith("http")
          ? favicon.href
          : new URL(favicon.href, baseUrl).href;
      }

      return null;
    }, website);

    return logoUrl;
  } catch (error) {
    logger.warn(`Failed to extract logo from ${website}: ${error.message}`);
    return null;
  } finally {
    await browser.close();
  }
}

// Ensure company logos are set for companies with websites
export async function ensureCompanyLogosFromWebsites(
  prisma: PrismaClient
): Promise<number> {
  const companiesWithWebsiteNoLogo = await prisma.company.findMany({
    where: {
      website: { not: null },
      logoUrl: null,
    },
    select: {
      id: true,
      name: true,
      website: true,
      domain: true,
    },
    take: 20, // Process in small batches to avoid overwhelming
  });

  let logosAdded = 0;
  let errorCount = 0;

  logger.info(
    `Found ${companiesWithWebsiteNoLogo.length} companies with websites but no logos`
  );

  for (const company of companiesWithWebsiteNoLogo) {
    try {
      if (!company.website) continue;

      // Try to extract logo from website
      let logoUrl = await extractCompanyLogoFromWebsite(company.website);

      // If no logo found, try using Clearbit Logo API as fallback
      if (!logoUrl && company.domain) {
        logoUrl = `https://logo.clearbit.com/${company.domain}`;
        logger.info(`Using Clearbit logo for ${company.name}: ${logoUrl}`);
      }

      if (logoUrl) {
        try {
          await prisma.company.update({
            where: { id: company.id },
            data: { logoUrl },
          });
          logger.info(`✅ Added logo to company ${company.name}: ${logoUrl}`);
          logosAdded++;
        } catch (updateError: any) {
          logger.warn(
            `Failed to update logo for company ${company.name}:`,
            updateError
          );
          errorCount++;
        }
      } else {
        logger.warn(`❌ No logo found for company ${company.name}`);
      }

      // Add delay between requests to be respectful
      await delay(3000, false);
    } catch (error) {
      logger.error(
        `Failed to process logo for company ${company.name}:`,
        error
      );
      errorCount++;
    }
  }

  logger.info(
    `Logo update summary: ${logosAdded} added, ${errorCount} errors, ${companiesWithWebsiteNoLogo.length - logosAdded - errorCount} skipped`
  );
  return logosAdded;
}
