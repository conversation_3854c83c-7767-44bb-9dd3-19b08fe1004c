// cron/lib/extractCompanyDomains.ts

import { PrismaClient } from "@prisma/client";
import dns from "dns";
import { promisify } from "util";
import { logger } from "../utils/logger";
import { chromium } from "playwright";
import { handleCaptchaIfPresent } from "../scripts/improvedCaptchaSolver";

const prisma = new PrismaClient();
const dnsLookup = promisify(dns.lookup);

/**
 * Extract and verify domains for multiple companies in batch
 * @param companies Array of company names or objects
 * @param batchSize Number of companies to process in parallel
 * @returns Map of company names to verified domains
 */
export async function batchExtractCompanyDomains(
  companies: Array<string | { name: string; id: string }>,
  batchSize = 5
): Promise<Map<string, string>> {
  const results = new Map<string, string>();
  const batches = chunk(companies, batchSize);

  logger.info(
    `Processing ${companies.length} companies in ${batches.length} batches of ${batchSize}`
  );

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    logger.info(`Processing batch ${i + 1}/${batches.length}`);

    // Process each batch in parallel
    const batchPromises = batch.map((company) => {
      const companyName = typeof company === "string" ? company : company.name;
      const companyId = typeof company === "string" ? null : company.id;

      return extractAndVerifyDomain(companyName, companyId || "");
    });

    const batchResults = await Promise.allSettled(batchPromises);

    // Process results
    batchResults.forEach((result, index) => {
      const company = batch[index];
      const companyName = typeof company === "string" ? company : company.name;

      if (result.status === "fulfilled" && result.value) {
        results.set(companyName, result.value);
        logger.info(`✅ Found domain for ${companyName}: ${result.value}`);
      } else {
        logger.info(`❌ Could not find domain for ${companyName}`);
      }
    });

    // Add a longer delay between batches to avoid rate limiting
    if (i < batches.length - 1) {
      const batchDelay = 10000; // 10 seconds between batches
      logger.info(
        `⏳ Waiting ${
          batchDelay / 1000
        } seconds before next batch to avoid rate limiting...`
      );
      await delay(batchDelay);
    }
  }

  return results;
}

/**
 * Extract and verify domain for a single company
 * @param companyName Company name
 * @param companyId Optional company ID for database updates
 * @returns Verified domain or null
 */
export async function extractAndVerifyDomain(
  companyName: string,
  companyId: string
): Promise<string | null> {
  logger.info(`Extracting domain for company: ${companyName}`);

  try {
    // Search for potential domains
    const potentialDomains = await searchCompanyDomains(companyName);

    if (potentialDomains.length === 0) {
      logger.warn(`No potential domains found for ${companyName}`);
      return null;
    }

    logger.info(
      `Found ${potentialDomains.length} potential domains for ${companyName}: ${potentialDomains.join(", ")}`
    );

    // Verify the first domain (most likely to be correct)
    const domain = potentialDomains[0];

    // Improved domain verification - accept domain if it appears valid
    // without requiring additional verification that might be failing
    if (domain && isValidDomainFormat(domain)) {
      logger.info(`✅ Verified domain for ${companyName}: ${domain}`);
      return domain;
    }

    logger.warn(`❌ No valid domains found for ${companyName}`);
    return null;
  } catch (error) {
    logger.error(`Error extracting domain for ${companyName}:`, error);
    return null;
  }
}

// Helper function to check if domain format is valid
function isValidDomainFormat(domain: string): boolean {
  // Basic domain format validation
  const domainRegex =
    /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
  return domainRegex.test(domain);
}

/**
 * Verify if a domain exists using DNS lookup
 * @param domain Domain to verify
 * @returns Whether the domain is valid
 */
async function verifyDomain(domain: string): Promise<boolean> {
  try {
    await dnsLookup(domain);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Extract root domain from a URL
 * @param url URL to extract domain from
 * @returns Root domain
 */
function extractRootDomain(url: string): string | null {
  try {
    // Add protocol if missing
    if (!url.startsWith("http")) {
      url = "https://" + url;
    }

    const urlObj = new URL(url);
    let hostname = urlObj.hostname;

    // Remove www. prefix
    hostname = hostname.replace(/^www\./, "");

    // Extract root domain (e.g., example.com from subdomain.example.com)
    const parts = hostname.split(".");
    if (parts.length > 2) {
      // Handle special cases like co.uk, com.au
      const tld = parts.slice(-2).join(".");
      if (["co.uk", "com.au", "co.nz", "org.uk"].includes(tld)) {
        return parts.slice(-3).join(".");
      }
      return parts.slice(-2).join(".");
    }

    return hostname;
  } catch (error) {
    return null;
  }
}

/**
 * Check if a domain is a common non-company domain
 * @param domain Domain to check
 * @returns Whether the domain is a common domain
 */
function isCommonDomain(domain: string): boolean {
  const commonDomains = [
    "google.com",
    "youtube.com",
    "facebook.com",
    "linkedin.com",
    "indeed.com",
    "glassdoor.com",
    "wikipedia.org",
    "yelp.com",
    "bloomberg.com",
    "crunchbase.com",
    "zoominfo.com",
    "bbb.org",
    "twitter.com",
    "instagram.com",
  ];

  return commonDomains.includes(domain);
}

/**
 * Split an array into chunks
 * @param array Array to split
 * @param size Chunk size
 * @returns Array of chunks
 */
function chunk<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

/**
 * Delay execution
 * @param ms Milliseconds to delay
 * @param randomize Whether to add a random component to the delay
 * @returns Promise that resolves after the delay
 */
function delay(ms: number, randomize = true): Promise<void> {
  // Add a random component to the delay to make it look more natural
  const randomDelay = randomize ? Math.floor(Math.random() * 2000) : 0; // 0-2 seconds random delay
  const totalDelay = ms + randomDelay;

  if (randomize && randomDelay > 0) {
    logger.debug(
      `Added random delay of ${randomDelay}ms (total: ${totalDelay}ms)`
    );
  }

  return new Promise((resolve) => setTimeout(resolve, totalDelay));
}

/**
 * Make a basic guess at a company domain from its name
 * @param companyName Company name
 * @returns Possible domain
 */
function guessBasicDomain(companyName: string): string | null {
  // Clean the company name
  const cleanName = companyName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, "") // Remove non-alphanumeric characters
    .trim();

  if (!cleanName) return null;

  // Return a basic .com domain
  return `${cleanName}.com`;
}

/**
 * Search Bing for company domains
 * @param companyName Company name to search for
 * @returns Array of potential domains
 */
export async function searchCompanyDomains(
  companyName: string
): Promise<string[]> {
  logger.info(`Searching Bing for company domain: ${companyName}`);

  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Construct Bing search query
    const searchQuery = encodeURIComponent(`${companyName} official website`);
    const bingUrl = `https://www.bing.com/search?q=${searchQuery}&setlang=en-US&cc=US&ensearch=1`;

    logger.info(`🌐 Navigating to Bing: ${bingUrl}`);

    await page.goto(bingUrl, {
      waitUntil: "domcontentloaded",
      timeout: 30000,
    });

    // Wait for search results to load
    await delay(3000);

    // Check for CAPTCHA
    const { captchaDetected } = await handleCaptchaIfPresent(page);
    if (captchaDetected) {
      logger.warn(`⚠️ CAPTCHA detected for company search: ${companyName}`);
      return [];
    }

    // Extract domains from search results
    const domains = await page.evaluate(() => {
      const results = Array.from(document.querySelectorAll(".b_algo"));
      return results
        .slice(0, 5)
        .map((result) => {
          const link = result.querySelector("cite")?.textContent || "";
          try {
            // Extract domain from URL
            const url = new URL(
              link.startsWith("http") ? link : `https://${link}`
            );
            return url.hostname.replace(/^www\./, "");
          } catch {
            // Try to extract domain from text if URL parsing fails
            const match = link.match(/(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
            return match ? match[1] : "";
          }
        })
        .filter(Boolean);
    });

    return [...new Set(domains)]; // Remove duplicates
  } catch (error) {
    logger.error(`Error searching Bing for company domain: ${error}`);
    return [];
  } finally {
    await browser.close();
  }
}

// Define the function to ensure websites are set based on domains
export async function ensureWebsiteFromDomain(
  prisma: PrismaClient
): Promise<number> {
  const companiesWithDomainNoWebsite = await prisma.company.findMany({
    where: {
      domain: { not: null },
      website: null,
    },
    select: {
      id: true,
      name: true,
      domain: true,
    },
  });

  let websitesAdded = 0;
  let errorCount = 0;

  logger.info(
    `Found ${companiesWithDomainNoWebsite.length} companies with domains but no websites`
  );

  for (const company of companiesWithDomainNoWebsite) {
    try {
      // Validate domain format before creating website
      if (!company.domain || !isValidDomainFormat(company.domain)) {
        logger.warn(
          `Invalid domain format for company ${company.name}: ${company.domain}`
        );
        continue;
      }

      const website = `https://www.${company.domain}`;

      try {
        await prisma.company.update({
          where: { id: company.id },
          data: { website },
        });
        logger.info(`Added website ${website} to company ${company.name}`);
        websitesAdded++;
      } catch (updateError: any) {
        // Check if this is a unique constraint error
        if (updateError.code === "P2002") {
          logger.warn(
            `Unique constraint error for company ${company.name} when adding website ${website}`
          );

          // Try to find the conflicting company
          const conflictingCompany = await prisma.company.findFirst({
            where: {
              website: website,
              id: { not: company.id },
            },
            select: { id: true, name: true },
          });

          if (conflictingCompany) {
            logger.warn(
              `Website ${website} already exists for company ${conflictingCompany.name}`
            );
          }

          errorCount++;
        } else {
          throw updateError;
        }
      }

      // Add a small delay between updates to avoid overwhelming the database
      await delay(1000, false);
    } catch (error) {
      logger.error(`Failed to add website for company ${company.name}:`, error);
      errorCount++;
    }
  }

  logger.info(
    `Website update summary: ${websitesAdded} added, ${errorCount} errors, ${companiesWithDomainNoWebsite.length - websitesAdded - errorCount} skipped`
  );
  return websitesAdded;
}

// Function to extract company logo from website
async function extractCompanyLogo(website: string): Promise<string | null> {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({
    userAgent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  });
  const page = await context.newPage();

  try {
    logger.info(`🎨 Extracting logo from: ${website}`);

    await page.goto(website, {
      waitUntil: "domcontentloaded",
      timeout: 15000,
    });

    // Wait for page to load
    await delay(2000);

    // Try multiple strategies to find the logo
    const logoUrl = await page.evaluate((siteUrl) => {
      const url = new URL(siteUrl);
      const baseUrl = `${url.protocol}//${url.hostname}`;

      // Strategy 1: Look for common logo selectors
      const logoSelectors = [
        'img[alt*="logo" i]',
        'img[class*="logo" i]',
        'img[id*="logo" i]',
        ".logo img",
        "#logo img",
        "header img",
        ".header img",
        ".navbar img",
        ".nav img",
        '[class*="brand"] img',
        '[class*="header"] img:first-of-type',
      ];

      for (const selector of logoSelectors) {
        const img = document.querySelector(selector) as HTMLImageElement;
        if (img && img.src) {
          // Make sure it's not a tiny icon or placeholder
          if (img.width >= 50 || img.height >= 30) {
            return img.src.startsWith("http")
              ? img.src
              : new URL(img.src, baseUrl).href;
          }
        }
      }

      // Strategy 2: Look for favicon as fallback
      const favicon = document.querySelector(
        'link[rel*="icon"]'
      ) as HTMLLinkElement;
      if (favicon && favicon.href) {
        return favicon.href.startsWith("http")
          ? favicon.href
          : new URL(favicon.href, baseUrl).href;
      }

      // Strategy 3: Try common logo paths
      const commonPaths = [
        "/logo.png",
        "/logo.svg",
        "/assets/logo.png",
        "/images/logo.png",
        "/img/logo.png",
        "/static/logo.png",
      ];

      // Return the first common path (we'll validate it exists later)
      return new URL(commonPaths[0], baseUrl).href;
    }, website);

    return logoUrl;
  } catch (error) {
    logger.warn(`Failed to extract logo from ${website}: ${error.message}`);
    return null;
  } finally {
    await browser.close();
  }
}

// Function to ensure logos are set for companies with websites
export async function ensureCompanyLogos(
  prisma: PrismaClient
): Promise<number> {
  const companiesWithWebsiteNoLogo = await prisma.company.findMany({
    where: {
      website: { not: null },
      logoUrl: null,
    },
    select: {
      id: true,
      name: true,
      website: true,
      domain: true,
    },
    take: 50, // Process in batches to avoid overwhelming
  });

  let logosAdded = 0;
  let errorCount = 0;

  logger.info(
    `Found ${companiesWithWebsiteNoLogo.length} companies with websites but no logos`
  );

  for (const company of companiesWithWebsiteNoLogo) {
    try {
      if (!company.website) continue;

      // Try to extract logo from website
      let logoUrl = await extractCompanyLogo(company.website);

      // If no logo found, try using Clearbit Logo API as fallback
      if (!logoUrl && company.domain) {
        logoUrl = `https://logo.clearbit.com/${company.domain}`;
        logger.info(`Using Clearbit logo for ${company.name}: ${logoUrl}`);
      }

      if (logoUrl) {
        try {
          await prisma.company.update({
            where: { id: company.id },
            data: { logoUrl },
          });
          logger.info(`Added logo ${logoUrl} to company ${company.name}`);
          logosAdded++;
        } catch (updateError: any) {
          logger.warn(
            `Failed to update logo for company ${company.name}:`,
            updateError
          );
          errorCount++;
        }
      } else {
        logger.warn(`No logo found for company ${company.name}`);
      }

      // Add delay between requests to be respectful
      await delay(3000, false);
    } catch (error) {
      logger.error(
        `Failed to process logo for company ${company.name}:`,
        error
      );
      errorCount++;
    }
  }

  logger.info(
    `Logo update summary: ${logosAdded} added, ${errorCount} errors, ${companiesWithWebsiteNoLogo.length - logosAdded - errorCount} skipped`
  );
  return logosAdded;
}
