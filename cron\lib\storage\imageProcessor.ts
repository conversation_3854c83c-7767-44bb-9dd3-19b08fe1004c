// cron/lib/storage/imageProcessor.ts
// Image processing service for optimization and resizing

import sharp from "sharp";
import { logger } from "../../utils/logger";
import { uploadFile, FileType, UploadResult } from "./r2Storage";

export interface ImageProcessingOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: "webp" | "png" | "jpeg";
  fit?: "cover" | "contain" | "fill" | "inside" | "outside";
}

export interface ProcessedImageResult {
  success: boolean;
  original?: UploadResult;
  optimized?: UploadResult;
  thumbnail?: UploadResult;
  error?: string;
}

// Predefined image sizes for different use cases
const IMAGE_SIZES = {
  companyLogo: {
    original: { width: 400, height: 200, quality: 90 },
    optimized: { width: 200, height: 100, quality: 80 },
    thumbnail: { width: 50, height: 25, quality: 70 },
  },
  profilePicture: {
    original: { width: 400, height: 400, quality: 90 },
    optimized: { width: 200, height: 200, quality: 80 },
    thumbnail: { width: 50, height: 50, quality: 70 },
  },
};

/**
 * Process and optimize an image buffer
 */
async function processImage(
  buffer: Buffer,
  options: ImageProcessingOptions
): Promise<{ buffer: Buffer; contentType: string }> {
  const {
    width,
    height,
    quality = 80,
    format = "webp",
    fit = "contain",
  } = options;

  let processor = sharp(buffer);

  // Resize if dimensions provided
  if (width || height) {
    processor = processor.resize(width, height, {
      fit,
      background: { r: 255, g: 255, b: 255, alpha: 0 }, // Transparent background
    });
  }

  // Convert format and set quality
  switch (format) {
    case "webp":
      processor = processor.webp({ quality });
      break;
    case "png":
      processor = processor.png({ quality });
      break;
    case "jpeg":
      processor = processor.jpeg({ quality });
      break;
  }

  const processedBuffer = await processor.toBuffer();
  const contentType = `image/${format}`;

  return { buffer: processedBuffer, contentType };
}

/**
 * Generate filename with size suffix
 */
function generateSizedFilename(originalName: string, suffix: string, format: string): string {
  const baseName = originalName.replace(/\.[^/.]+$/, ""); // Remove extension
  return `${baseName}-${suffix}.${format}`;
}

/**
 * Process company logo with multiple sizes
 */
export async function processCompanyLogo(
  buffer: Buffer,
  originalName: string,
  companyId: string
): Promise<ProcessedImageResult> {
  try {
    logger.info(`🖼️ Processing company logo: ${originalName} for company ${companyId}`);

    const sizes = IMAGE_SIZES.companyLogo;
    const results: ProcessedImageResult = { success: true };

    // Process original size
    try {
      const { buffer: originalBuffer, contentType: originalContentType } = await processImage(buffer, {
        ...sizes.original,
        format: "webp",
      });

      results.original = await uploadFile(
        originalBuffer,
        generateSizedFilename(originalName, "original", "webp"),
        originalContentType,
        "companyLogos",
        companyId
      );

      if (!results.original.success) {
        logger.warn(`Failed to upload original logo: ${results.original.error}`);
      }
    } catch (error) {
      logger.warn(`Failed to process original logo: ${error}`);
    }

    // Process optimized size
    try {
      const { buffer: optimizedBuffer, contentType: optimizedContentType } = await processImage(buffer, {
        ...sizes.optimized,
        format: "webp",
      });

      results.optimized = await uploadFile(
        optimizedBuffer,
        generateSizedFilename(originalName, "optimized", "webp"),
        optimizedContentType,
        "companyLogos",
        companyId
      );

      if (!results.optimized.success) {
        logger.warn(`Failed to upload optimized logo: ${results.optimized.error}`);
      }
    } catch (error) {
      logger.warn(`Failed to process optimized logo: ${error}`);
    }

    // Process thumbnail size
    try {
      const { buffer: thumbnailBuffer, contentType: thumbnailContentType } = await processImage(buffer, {
        ...sizes.thumbnail,
        format: "webp",
      });

      results.thumbnail = await uploadFile(
        thumbnailBuffer,
        generateSizedFilename(originalName, "thumbnail", "webp"),
        thumbnailContentType,
        "companyLogos",
        companyId
      );

      if (!results.thumbnail.success) {
        logger.warn(`Failed to upload thumbnail logo: ${results.thumbnail.error}`);
      }
    } catch (error) {
      logger.warn(`Failed to process thumbnail logo: ${error}`);
    }

    // Check if at least one size was successful
    const hasSuccess = results.original?.success || results.optimized?.success || results.thumbnail?.success;
    
    if (!hasSuccess) {
      return {
        success: false,
        error: "Failed to process any image sizes",
      };
    }

    logger.info(`✅ Company logo processed successfully: ${originalName}`);
    return results;
  } catch (error) {
    logger.error(`❌ Failed to process company logo ${originalName}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown processing error",
    };
  }
}

/**
 * Process profile picture with multiple sizes
 */
export async function processProfilePicture(
  buffer: Buffer,
  originalName: string,
  userId: string
): Promise<ProcessedImageResult> {
  try {
    logger.info(`🖼️ Processing profile picture: ${originalName} for user ${userId}`);

    const sizes = IMAGE_SIZES.profilePicture;
    const results: ProcessedImageResult = { success: true };

    // Process original size
    const { buffer: originalBuffer, contentType: originalContentType } = await processImage(buffer, {
      ...sizes.original,
      format: "webp",
    });

    results.original = await uploadFile(
      originalBuffer,
      generateSizedFilename(originalName, "original", "webp"),
      originalContentType,
      "profilePictures",
      userId
    );

    // Process optimized size
    const { buffer: optimizedBuffer, contentType: optimizedContentType } = await processImage(buffer, {
      ...sizes.optimized,
      format: "webp",
    });

    results.optimized = await uploadFile(
      optimizedBuffer,
      generateSizedFilename(originalName, "optimized", "webp"),
      optimizedContentType,
      "profilePictures",
      userId
    );

    // Process thumbnail size
    const { buffer: thumbnailBuffer, contentType: thumbnailContentType } = await processImage(buffer, {
      ...sizes.thumbnail,
      format: "webp",
    });

    results.thumbnail = await uploadFile(
      thumbnailBuffer,
      generateSizedFilename(originalName, "thumbnail", "webp"),
      thumbnailContentType,
      "profilePictures",
      userId
    );

    logger.info(`✅ Profile picture processed successfully: ${originalName}`);
    return results;
  } catch (error) {
    logger.error(`❌ Failed to process profile picture ${originalName}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown processing error",
    };
  }
}

/**
 * Download image from URL and process it
 */
export async function downloadAndProcessImage(
  imageUrl: string,
  fileName: string,
  companyId: string
): Promise<ProcessedImageResult> {
  try {
    logger.info(`📥 Downloading and processing image from: ${imageUrl}`);

    // Download image
    const response = await fetch(imageUrl, {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    });

    if (!response.ok) {
      return {
        success: false,
        error: `Failed to download image: ${response.status} ${response.statusText}`,
      };
    }

    const buffer = Buffer.from(await response.arrayBuffer());

    // Process the image
    return await processCompanyLogo(buffer, fileName, companyId);
  } catch (error) {
    logger.error(`❌ Failed to download and process image ${imageUrl}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
