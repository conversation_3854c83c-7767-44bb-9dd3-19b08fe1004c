// cron/lib/storage/r2Storage.ts
// Cloudflare R2 storage service for file uploads and management

import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
import { logger } from "../../utils/logger";
import crypto from "crypto";
import path from "path";

// R2 Configuration
const R2_CONFIG = {
  endpoint:
    process.env.R2_ENDPOINT ||
    "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com",
  region: "auto", // R2 uses 'auto' as region
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || "",
  },
};

// Initialize R2 client
const r2Client = new S3Client(R2_CONFIG);

// Bucket name from environment
const BUCKET_NAME = process.env.R2_BUCKET_NAME || "auto-apply-assets";

// File type configurations
const FILE_CONFIGS = {
  companyLogos: {
    folder: "company-logos",
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: [
      "image/png",
      "image/jpeg",
      "image/jpg",
      "image/webp",
      "image/svg+xml",
    ],
  },
  resumes: {
    folder: "resumes",
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ],
  },
  profilePictures: {
    folder: "profile-pictures",
    maxSize: 2 * 1024 * 1024, // 2MB
    allowedTypes: ["image/png", "image/jpeg", "image/jpg", "image/webp"],
  },
};

export type FileType = keyof typeof FILE_CONFIGS;

export interface UploadResult {
  success: boolean;
  fileKey?: string;
  publicUrl?: string;
  error?: string;
  fileSize?: number;
  contentType?: string;
}

export interface FileMetadata {
  originalName: string;
  contentType: string;
  size: number;
  uploadedAt: Date;
  folder: string;
}

/**
 * Generate a unique file key for storage
 */
function generateFileKey(
  fileType: FileType,
  originalName: string,
  companyId?: string
): string {
  const config = FILE_CONFIGS[fileType];
  const timestamp = Date.now();
  const randomId = crypto.randomBytes(8).toString("hex");
  const extension = path.extname(originalName).toLowerCase();

  // Create a clean filename
  const baseName = path
    .basename(originalName, extension)
    .replace(/[^a-zA-Z0-9-_]/g, "-")
    .toLowerCase()
    .substring(0, 50);

  if (companyId) {
    return `${config.folder}/${companyId}/${timestamp}-${randomId}-${baseName}${extension}`;
  }

  return `${config.folder}/${timestamp}-${randomId}-${baseName}${extension}`;
}

/**
 * Get public URL for a file
 */
export function getPublicUrl(fileKey: string): string {
  const customDomain = process.env.R2_CUSTOM_DOMAIN;
  if (customDomain) {
    return `https://${customDomain}/${fileKey}`;
  }

  // Fallback to R2 public URL
  const accountId = process.env.R2_ACCOUNT_ID;
  return `https://pub-${accountId}.r2.dev/${fileKey}`;
}

/**
 * Validate file before upload
 */
function validateFile(
  buffer: Buffer,
  contentType: string,
  fileType: FileType
): { valid: boolean; error?: string } {
  const config = FILE_CONFIGS[fileType];

  // Check file size
  if (buffer.length > config.maxSize) {
    return {
      valid: false,
      error: `File size ${buffer.length} exceeds maximum ${config.maxSize} bytes for ${fileType}`,
    };
  }

  // Check content type
  if (!config.allowedTypes.includes(contentType)) {
    return {
      valid: false,
      error: `Content type ${contentType} not allowed for ${fileType}. Allowed: ${config.allowedTypes.join(", ")}`,
    };
  }

  return { valid: true };
}

/**
 * Upload a file to R2 storage
 */
export async function uploadFile(
  buffer: Buffer,
  originalName: string,
  contentType: string,
  fileType: FileType,
  companyId?: string
): Promise<UploadResult> {
  try {
    // Validate file
    const validation = validateFile(buffer, contentType, fileType);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error,
      };
    }

    // Generate unique file key
    const fileKey = generateFileKey(fileType, originalName, companyId);

    // Prepare upload command
    const uploadCommand = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: fileKey,
      Body: buffer,
      ContentType: contentType,
      Metadata: {
        originalName,
        uploadedAt: new Date().toISOString(),
        fileType,
        ...(companyId && { companyId }),
      },
    });

    // Upload to R2
    await r2Client.send(uploadCommand);

    const publicUrl = getPublicUrl(fileKey);

    logger.info(
      `✅ File uploaded successfully: ${fileKey} (${buffer.length} bytes)`
    );

    return {
      success: true,
      fileKey,
      publicUrl,
      fileSize: buffer.length,
      contentType,
    };
  } catch (error) {
    logger.error(`❌ Failed to upload file ${originalName}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown upload error",
    };
  }
}

/**
 * Download a file from R2 storage
 */
export async function downloadFile(
  fileKey: string
): Promise<{ success: boolean; buffer?: Buffer; error?: string }> {
  try {
    const downloadCommand = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: fileKey,
    });

    const response = await r2Client.send(downloadCommand);

    if (!response.Body) {
      return {
        success: false,
        error: "No file content received",
      };
    }

    // Convert stream to buffer
    const chunks: Uint8Array[] = [];
    const reader = response.Body.transformToWebStream().getReader();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      chunks.push(value);
    }

    const buffer = Buffer.concat(chunks);

    logger.info(
      `✅ File downloaded successfully: ${fileKey} (${buffer.length} bytes)`
    );

    return {
      success: true,
      buffer,
    };
  } catch (error) {
    logger.error(`❌ Failed to download file ${fileKey}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown download error",
    };
  }
}

/**
 * Delete a file from R2 storage
 */
export async function deleteFile(
  fileKey: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const deleteCommand = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: fileKey,
    });

    await r2Client.send(deleteCommand);

    logger.info(`✅ File deleted successfully: ${fileKey}`);

    return { success: true };
  } catch (error) {
    logger.error(`❌ Failed to delete file ${fileKey}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown delete error",
    };
  }
}

/**
 * Download image from URL and upload to R2
 */
export async function downloadAndUploadImage(
  imageUrl: string,
  fileName: string,
  fileType: FileType,
  companyId?: string
): Promise<UploadResult> {
  try {
    logger.info(`📥 Downloading image from: ${imageUrl}`);

    // Download image
    const response = await fetch(imageUrl, {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    });

    if (!response.ok) {
      return {
        success: false,
        error: `Failed to download image: ${response.status} ${response.statusText}`,
      };
    }

    const buffer = Buffer.from(await response.arrayBuffer());
    const contentType = response.headers.get("content-type") || "image/png";

    // Upload to R2
    return await uploadFile(buffer, fileName, contentType, fileType, companyId);
  } catch (error) {
    logger.error(`❌ Failed to download and upload image ${imageUrl}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
