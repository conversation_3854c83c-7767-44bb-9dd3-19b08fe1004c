{"name": "auto-apply-cron", "type": "module", "scripts": {"start": "tsx index.ts", "check-exp": "tsx scripts/checkExperienceRequirements.ts", "update-matches": "tsx jobs/updateJobMatches.ts", "market-analytics": "tsx jobs/marketAnalyticsCollector.ts", "enrich-jobs": "tsx jobs/enrichJobDetails.ts", "fetch-collections": "tsx jobs/fetchStoreLinkedInCategories.ts", "fetch-skills": "tsx jobs/saveSkills.ts", "fetch-occupations": "tsx jobs/saveOccupations.ts", "fetch-schools": "tsx jobs/loadSchools.ts", "fetch-locations": "tsx jobs/loadLocations.ts", "fetch-languages": "tsx scripts/loadLanguages.ts", "fetch-cities": "tsx scripts/loadCities.ts", "login": "tsx scripts/saveLinkedInSession.ts", "update-domains": "tsx scripts/updateCompanyDomains.ts", "enrich-company-data": "tsx jobs/enrichCompanyData.ts", "daily-summary": "tsx scripts/sendDailySummary.ts", "reset-circuit-breaker": "tsx scripts/run-with-gc.ts scripts/reset-circuit-breaker.ts", "auto-reset-circuit-breaker": "tsx scripts/reset-circuit-breaker-if-idle.ts", "update-resource-manager": "tsx scripts/update-resource-manager.ts", "dedupe-occupations": "tsx scripts/deduplicate-occupations.ts", "postinstall": "npx playwright install --with-deps"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@prisma/client": "^6.8.2", "axios": "^1.8.4", "cheerio": "^1.0.0", "cron": "^4.2.0", "ioredis": "^5.6.1", "jsdom": "^24.1.3", "natural": "^8.0.1", "node-fetch": "^3.3.2", "node-os-utils": "^1.3.7", "p-limit": "^4.0.0", "playwright": "^1.43.1", "playwright-extra": "^4.3.6", "pug": "^3.0.3", "puppeteer-extra-plugin-stealth": "^2.11.2", "resend": "^4.3.0", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/node": "^22.14.0", "@types/node-fetch": "^2.6.12", "@types/qs": "^6.9.18", "@types/uuid": "^9.0.8", "prisma": "^6.8.2", "typescript": "^5.3.3"}}