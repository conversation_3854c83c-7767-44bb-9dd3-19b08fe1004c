// cron/scripts/reset-circuit-breaker.ts
// <PERSON><PERSON><PERSON> to reset the circuit breaker and restart the scheduler service

import { logger } from "../utils/logger";
import { CircuitBreaker } from "../utils/circuitBreaker";
import { startScheduledJobs, stopScheduledJobs } from "../jobs/scheduledJobs";
import { spawn } from "child_process";
import { redis } from "../utils/redis.js";
import os from "os";

// Function to get detailed system resource information
function getSystemResourceInfo() {
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;

  const cpuCount = os.cpus().length;
  const loadAvg = os.loadavg()[0]; // 1 minute load average
  const loadPerCpu = loadAvg / cpuCount;
  const cpuUsagePercent = loadPerCpu * 100;

  return {
    memory: {
      total: formatBytes(totalMemory),
      free: formatBytes(freeMemory),
      used: formatBytes(usedMemory),
      usagePercent: memoryUsagePercent.toFixed(2) + "%",
    },
    cpu: {
      count: cpuCount,
      loadAverage: loadAvg.toFixed(2),
      loadPerCpu: loadPerCpu.toFixed(2),
      usagePercent: cpuUsagePercent.toFixed(2) + "%",
    },
  };
}

// Helper function to format bytes to human-readable format
function formatBytes(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// Try to force garbage collection if available
function forceGarbageCollection() {
  if (global.gc) {
    logger.info("🧹 Running forced garbage collection...");

    // Get memory usage before GC
    const beforeMemory = process.memoryUsage();

    // Run garbage collection
    global.gc();

    // Get memory usage after GC
    const afterMemory = process.memoryUsage();

    // Calculate freed memory
    const freedHeapMemory = beforeMemory.heapUsed - afterMemory.heapUsed;
    const freedRssMemory = beforeMemory.rss - afterMemory.rss;

    logger.info(
      `✅ Garbage collection complete. Freed ${formatBytes(freedHeapMemory)} of heap memory and ${formatBytes(freedRssMemory)} of RSS memory`
    );
    return true;
  } else {
    // Don't log warning - GC not being available is normal on servers
    return false;
  }
}

// Function to reset running job states and force restart
async function resetRunningJobStates() {
  logger.info("� Resetting running job states...");

  try {
    // Get running jobs from Redis
    const runningJobs = await redis.hgetall("cron:running_jobs");
    const runningJobsList = Object.entries(runningJobs).filter(
      ([job, status]) => status === "true"
    );

    if (runningJobsList.length === 0) {
      logger.info("✅ No jobs currently marked as running");
      return [];
    }

    logger.info(
      `🔍 Found ${runningJobsList.length} jobs marked as running: ${runningJobsList.map(([job]) => job).join(", ")}`
    );

    // Clear all running job statuses to reset them
    await redis.del("cron:running_jobs");
    logger.info("✅ Cleared all running job statuses from Redis");

    // Return the list of jobs that were running so we can restart them
    return runningJobsList.map(([job]) => job);
  } catch (error) {
    logger.error("❌ Error resetting running job states:", error);
    return [];
  }
}

// Function to kill any running job processes
async function killRunningJobs() {
  logger.info("🔪 Killing any running job processes...");

  try {
    const { exec } = await import("child_process");
    const { promisify } = await import("util");
    const execAsync = promisify(exec);

    // Get running Node.js processes that might be our jobs
    const platform = process.platform;
    let command = "";

    if (platform === "win32") {
      // Windows - find Node.js processes with tsx or npm
      command = 'tasklist /FI "IMAGENAME eq node.exe" /FO CSV | findstr tsx';
    } else {
      // Linux/Unix/macOS - find processes with tsx or npm run
      command = "ps aux | grep -E '(tsx|npm run)' | grep -v grep";
    }

    try {
      const { stdout } = await execAsync(command);

      if (stdout.trim()) {
        logger.info(`🔍 Found running processes: ${stdout.trim()}`);

        // Extract PIDs and kill them
        if (platform === "win32") {
          // Windows CSV format parsing would be more complex
          logger.info(
            "⚠️ Windows process killing not implemented, relying on Redis state reset"
          );
        } else {
          // Unix-like systems
          const lines = stdout.trim().split("\n");
          for (const line of lines) {
            const parts = line.trim().split(/\s+/);
            if (parts.length >= 2) {
              const pid = parts[1];
              try {
                process.kill(parseInt(pid), "SIGTERM");
                logger.info(`🔪 Killed process ${pid}`);
              } catch (killError) {
                logger.warn(`⚠️ Could not kill process ${pid}: ${killError}`);
              }
            }
          }
        }
      } else {
        logger.info("✅ No running job processes found");
      }
    } catch (execError) {
      logger.info("✅ No running job processes found (or command failed)");
    }

    // Also clear Redis job states as a backup
    await resetRunningJobStates();
  } catch (error) {
    logger.error("❌ Error killing running jobs:", error);
  }
}

// Function to restart specific jobs that were running
async function restartSpecificJobs(jobsToRestart: string[]) {
  if (jobsToRestart.length === 0) {
    logger.info("✅ No specific jobs to restart");
    return;
  }

  logger.info(
    `🚀 Attempting to restart ${jobsToRestart.length} jobs that were previously running: ${jobsToRestart.join(", ")}`
  );

  // Map job names to npm script commands
  const jobCommands: Record<string, string> = {
    checkExperienceRequirements: "check-exp",
    domainUpdate: "update-domains",
    updateJobMatches: "update-matches",
    dailySummary: "daily-summary",
    marketAnalytics: "market-analytics",
    enrichJobDetails: "enrich-jobs",
  };

  for (const jobName of jobsToRestart) {
    const command = jobCommands[jobName];
    if (command) {
      logger.info(`🔄 Restarting job: ${jobName} (npm run ${command})`);

      try {
        // Start the job in the background
        const child = spawn("npm", ["run", command], {
          cwd: process.cwd(),
          stdio: "pipe",
          detached: true,
        });

        // Don't wait for the job to complete, just start it
        child.unref();

        logger.info(`✅ Started ${jobName} successfully`);
      } catch (error) {
        logger.error(`❌ Failed to restart ${jobName}:`, error);
      }
    } else {
      logger.warn(`⚠️ Unknown job type: ${jobName}, skipping restart`);
    }
  }

  logger.info("✅ Job restart process completed");
}

// Main function to reset the circuit breaker
async function resetCircuitBreaker() {
  logger.info("🔄 Starting circuit breaker reset...");

  try {
    // Check system resources before reset
    const beforeResources = getSystemResourceInfo();
    logger.info(
      `📊 System resources before reset: ${JSON.stringify(beforeResources, null, 2)}`
    );

    // Try to free up memory
    forceGarbageCollection();

    // Create a new circuit breaker instance
    const circuitBreaker = new CircuitBreaker({
      memoryThresholdPercent: 85,
      cpuThresholdPercent: 85,
      errorThresholdCount: 5,
      resetTimeoutMs: 10000, // 10 seconds
      checkIntervalMs: 5000, // 5 seconds
      onStateChange: (oldState, newState) => {
        logger.info(
          `🔄 Circuit breaker state changed from ${oldState} to ${newState}`
        );
      },
    });

    // Log initial state
    logger.info(
      `🔄 Initial circuit breaker state: ${circuitBreaker.getState()}`
    );

    // Force close the circuit
    logger.info("🔄 Forcing circuit breaker to closed state...");
    circuitBreaker.closeCircuit();

    // Log state after force close
    logger.info(
      `🔄 Circuit breaker state after force close: ${circuitBreaker.getState()}`
    );

    // Check system resources after reset
    const afterResources = getSystemResourceInfo();
    logger.info(
      `📊 System resources after reset: ${JSON.stringify(afterResources, null, 2)}`
    );

    // Stop the circuit breaker monitoring
    circuitBreaker.stopMonitoring();

    // Kill any actually running jobs first
    await killRunningJobs();

    // Stop any running scheduled jobs
    logger.info("🛑 Stopping any running scheduled jobs...");
    stopScheduledJobs();

    // Wait a moment for jobs to stop
    await new Promise((resolve) => setTimeout(resolve, 5000));

    // Restart scheduled jobs
    logger.info("🚀 Restarting scheduled jobs...");
    startScheduledJobs();

    logger.info(
      "✅ Circuit breaker reset and scheduled jobs restarted successfully"
    );

    // Log next steps
    logger.info("📋 Next steps:");
    logger.info("1. Monitor the logs to ensure jobs are running correctly");
    logger.info("2. Check the scheduler status with 'npm run check-scheduler'");
    logger.info(
      "3. If problems persist, consider restarting the entire scheduler service"
    );
  } catch (error) {
    logger.error("❌ Error resetting circuit breaker:", error);
  }
}

// Run the reset
resetCircuitBreaker()
  .then(() => {
    logger.info("✅ Circuit breaker reset completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Circuit breaker reset failed with error:", error);
    process.exit(1);
  });
