<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Carousel from '$lib/components/ui/carousel/index.js';

  import Autoplay from 'embla-carousel-autoplay';
  import type { AlignmentOptionType } from 'embla-carousel/components/Alignment';

  interface FeaturedCompany {
    id: string;
    name: string;
    logoUrl: string | null;
    companySize: string | null;
    companyStage: string | null;
    activeJobCount: number | null;
  }

  interface CategorizedCompanies {
    startups: FeaturedCompany[];
    growth: FeaturedCompany[];
    enterprise: FeaturedCompany[];
  }

  // Reactive state using Svelte 5 patterns
  let companies = $state<CategorizedCompanies>({
    startups: [],
    growth: [],
    enterprise: [],
  });
  let loading = $state(true);

  // Create autoplay plugins with different settings for each carousel
  const plugin1 = Autoplay({ delay: 2000, stopOnInteraction: true });
  const plugin2 = Autoplay({ delay: 4000, stopOnInteraction: true });
  const plugin3 = Autoplay({ delay: 2500, stopOnInteraction: true });

  // Set reverse direction for the middle carousel
  const options1 = { align: 'start' as AlignmentOptionType, loop: true };
  const options2 = { align: 'start' as AlignmentOptionType, loop: true };
  const options3 = { align: 'start' as const, loop: true };

  // Fetch companies data
  async function fetchCompanies() {
    try {
      const response = await fetch('/api/companies/featured');
      if (response.ok) {
        companies = await response.json();
      } else {
        console.error('Failed to fetch featured companies');
      }
    } catch (error) {
      console.error('Error fetching featured companies:', error);
    } finally {
      loading = false;
    }
  }

  // Initialize data fetch
  fetchCompanies();

  function getCompanyLogo(company: FeaturedCompany): string {
    return (
      company.logoUrl ||
      `https://placehold.co/200x80/f1f5f9/64748b?text=${encodeURIComponent(company.name)}`
    );
  }
</script>

<section class="bg-muted py-12">
  <div class="px-4">
    {#if loading}
      <div class="flex items-center justify-center py-16">
        <div class="text-muted-foreground">Loading companies...</div>
      </div>
    {:else}
      <!-- Startups row - left to right -->
      <div class="mb-8">
        <h3 class="text-muted-foreground mb-4 text-center text-lg font-semibold">
          Innovative Startups
        </h3>
        <Carousel.Root plugins={[plugin1]} opts={options1} class="w-full">
          <Carousel.Content>
            {#each companies.startups as company (company.id)}
              <Carousel.Item class="md:basis-1/4 lg:basis-1/6">
                <div class="p-1">
                  <Card.Root
                    class="hover:border-primary/10 transition-all duration-300 hover:shadow-md">
                    <Card.Content class="flex h-24 items-center justify-center p-6">
                      <img
                        src={getCompanyLogo(company)}
                        alt="{company.name} logo"
                        class="max-h-12 max-w-[120px] opacity-70 transition-all duration-300 hover:scale-105 hover:opacity-100 dark:opacity-60 dark:hover:opacity-90"
                        onerror={(e) =>
                          ((e.currentTarget as HTMLImageElement).src =
                            `https://placehold.co/200x80/f1f5f9/64748b?text=${encodeURIComponent(company.name)}`)} />
                    </Card.Content>
                  </Card.Root>
                </div>
              </Carousel.Item>
            {/each}
          </Carousel.Content>
        </Carousel.Root>
      </div>

      <!-- Growth companies row - right to left -->
      <div class="mb-8">
        <h3 class="text-muted-foreground mb-4 text-center text-lg font-semibold">
          Growing Companies
        </h3>
        <Carousel.Root plugins={[plugin2]} opts={options2} class="w-full">
          <Carousel.Content>
            {#each companies.growth as company (company.id)}
              <Carousel.Item class="md:basis-1/4 lg:basis-1/6">
                <div class="p-1">
                  <Card.Root
                    class="hover:border-primary/10 transition-all duration-300 hover:shadow-md">
                    <Card.Content class="flex h-24 items-center justify-center p-6">
                      <img
                        src={getCompanyLogo(company)}
                        alt="{company.name} logo"
                        class="max-h-12 max-w-[120px] opacity-70 transition-all duration-300 hover:scale-105 hover:opacity-100 dark:opacity-60 dark:hover:opacity-90"
                        onerror={(e) =>
                          ((e.currentTarget as HTMLImageElement).src =
                            `https://placehold.co/200x80/f1f5f9/64748b?text=${encodeURIComponent(company.name)}`)} />
                    </Card.Content>
                  </Card.Root>
                </div>
              </Carousel.Item>
            {/each}
          </Carousel.Content>
        </Carousel.Root>
      </div>

      <!-- Enterprise companies row - left to right -->
      <div class="mb-8">
        <h3 class="text-muted-foreground mb-4 text-center text-lg font-semibold">
          Enterprise Leaders
        </h3>
        <Carousel.Root plugins={[plugin3]} opts={options3} class="w-full">
          <Carousel.Content>
            {#each companies.enterprise as company, i (company.id)}
              <Carousel.Item class="md:basis-1/4 lg:basis-1/6">
                <div class="p-1">
                  <Card.Root
                    class="hover:border-primary/10 transition-all duration-300 hover:shadow-md">
                    <Card.Content class="flex h-24 items-center justify-center p-6">
                      <img
                        src={getCompanyLogo(company)}
                        alt="{company.name} logo"
                        class="max-h-12 max-w-[120px] opacity-70 transition-all duration-300 hover:scale-105 hover:opacity-100 dark:opacity-60 dark:hover:opacity-90"
                        on:error={(e) =>
                          ((e.currentTarget as HTMLImageElement).src =
                            `https://placehold.co/200x80/f1f5f9/64748b?text=${encodeURIComponent(company.name)}`)} />
                    </Card.Content>
                  </Card.Root>
                </div>
              </Carousel.Item>
            {/each}
          </Carousel.Content>
        </Carousel.Root>
      </div>

      <p class="text-md text-muted-foreground mt-8 text-center">
        Join thousands of professionals who've found their dream jobs through our platform
      </p>
    {/if}
  </div>
</section>
