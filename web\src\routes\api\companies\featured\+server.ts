import { json } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

interface FeaturedCompany {
  id: string;
  name: string;
  logoUrl: string | null;
  companySize: string | null;
  companyStage: string | null;
  activeJobCount: number | null;
}

interface CategorizedCompanies {
  startups: FeaturedCompany[];
  growth: FeaturedCompany[];
  enterprise: FeaturedCompany[];
}

export const GET: RequestHandler = async () => {
  try {
    // Check if prisma client is available
    if (!prisma) {
      console.error('Prisma client is not initialized');
      return json({ error: 'Database connection not available' }, { status: 500 });
    }

    // Get companies with active jobs (logos will be handled by fallback)
    const companies = await prisma.company.findMany({
      where: {
        activeJobCount: { gt: 0 },
      },
      select: {
        id: true,
        name: true,
        logoUrl: true,
        companySize: true,
        companyStage: true,
        activeJobCount: true,
      },
      orderBy: [{ activeJobCount: 'desc' }, { name: 'asc' }],
      take: 60, // Get top 60 companies to distribute across categories
    });

    // Categorize companies by size
    const categorized: CategorizedCompanies = {
      startups: [],
      growth: [],
      enterprise: [],
    };

    companies.forEach((company) => {
      const size = company.companySize;

      if (size === 'SIZE_1_10' || size === 'SIZE_11_50') {
        categorized.startups.push(company);
      } else if (size === 'SIZE_51_200' || size === 'SIZE_201_500') {
        categorized.growth.push(company);
      } else if (
        size === 'SIZE_501_1000' ||
        size === 'SIZE_1001_5000' ||
        size === 'SIZE_5001_10000' ||
        size === 'SIZE_10000_PLUS'
      ) {
        categorized.enterprise.push(company);
      } else {
        // If no size specified, distribute based on job count
        if (company.activeJobCount && company.activeJobCount > 100) {
          categorized.enterprise.push(company);
        } else if (company.activeJobCount && company.activeJobCount > 20) {
          categorized.growth.push(company);
        } else {
          categorized.startups.push(company);
        }
      }
    });

    // Ensure each category has at least some companies by redistributing if needed
    const minPerCategory = 8;

    // If any category is too small, fill from others
    if (categorized.startups.length < minPerCategory) {
      const needed = minPerCategory - categorized.startups.length;
      const fromGrowth = categorized.growth.splice(0, Math.min(needed, categorized.growth.length));
      categorized.startups.push(...fromGrowth);
    }

    if (categorized.growth.length < minPerCategory) {
      const needed = minPerCategory - categorized.growth.length;
      const fromEnterprise = categorized.enterprise.splice(
        0,
        Math.min(needed, categorized.enterprise.length)
      );
      categorized.growth.push(...fromEnterprise);
    }

    if (categorized.enterprise.length < minPerCategory) {
      const needed = minPerCategory - categorized.enterprise.length;
      const fromStartups = categorized.startups.splice(
        -Math.min(needed, categorized.startups.length)
      );
      categorized.enterprise.push(...fromStartups);
    }

    // Limit each category to reasonable size for carousel
    const maxPerCategory = 20;
    categorized.startups = categorized.startups.slice(0, maxPerCategory);
    categorized.growth = categorized.growth.slice(0, maxPerCategory);
    categorized.enterprise = categorized.enterprise.slice(0, maxPerCategory);

    return json(categorized);
  } catch (error) {
    console.error('Error fetching featured companies:', error);
    return json({ error: 'Failed to fetch featured companies' }, { status: 500 });
  }
};
